<div class="bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-black">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="<?php echo e(route('products.index')); ?>" class="ml-1 text-sm font-medium text-gray-700 hover:text-black md:ml-2">Products</a>
                    </div>
                </li>
                <?php if($product->category): ?>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="<?php echo e(route('products.category', $product->category->slug)); ?>" class="ml-1 text-sm font-medium text-gray-700 hover:text-black md:ml-2"><?php echo e($product->category->name); ?></a>
                    </div>
                </li>
                <?php endif; ?>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2"><?php echo e($product->name); ?></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Product Details -->
        <div class="lg:grid lg:grid-cols-2 lg:gap-x-8 lg:items-start">
            <!-- Image gallery -->
            <div class="flex flex-col-reverse">
                <!-- Image selector -->
                <div class="hidden mt-6 w-full max-w-2xl mx-auto sm:block lg:max-w-none">
                    <div class="grid grid-cols-4 gap-6">
                        <!-- Additional images would go here -->
                        <button class="relative h-24 bg-white rounded-md flex items-center justify-center text-sm font-medium uppercase text-gray-900 cursor-pointer hover:bg-gray-50 focus:outline-none focus:ring focus:ring-offset-4 focus:ring-black">
                            <span class="sr-only">Main image</span>
                            <span class="absolute inset-0 rounded-md overflow-hidden">
                                <img src="<?php echo e($product->image_url); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-full object-center object-cover">
                            </span>
                        </button>
                    </div>
                </div>

                <!-- Main image -->
                <div class="w-full aspect-w-1 aspect-h-1">
                    <img src="<?php echo e($product->image_url); ?>" alt="<?php echo e($product->name); ?>" class="w-full h-full object-center object-cover sm:rounded-lg">
                </div>
            </div>

            <!-- Product info -->
            <div class="mt-10 px-4 sm:px-0 sm:mt-16 lg:mt-0">
                <h1 class="text-3xl font-bold tracking-tight text-gray-900"><?php echo e($product->name); ?></h1>

                <div class="mt-3">
                    <h2 class="sr-only">Product information</h2>
                    <p class="text-3xl tracking-tight text-gray-900">
                        ₦<?php echo e(number_format($selectedVariant ? $selectedVariant->price : $product->getCurrentPrice(), 2)); ?>

                    </p>
                    <?php if($product->isOnSale() && $product->discount_price): ?>
                        <p class="text-xl text-gray-500 line-through ml-2">₦<?php echo e(number_format($product->price, 2)); ?></p>
                    <?php endif; ?>
                </div>

                <!-- Reviews -->
                <div class="mt-3">
                    <h3 class="sr-only">Reviews</h3>
                    <div class="flex items-center">
                        <div class="flex items-center">
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <svg class="w-5 h-5 <?php echo e($i <= ($product->averageRating() ?? 0) ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            <?php endfor; ?>
                        </div>
                        <p class="ml-3 text-sm text-gray-500"><?php echo e($product->reviewCount()); ?> reviews</p>
                    </div>
                </div>

                <div class="mt-6">
                    <h3 class="sr-only">Description</h3>
                    <div class="text-base text-gray-700 space-y-6">
                        <p><?php echo e($product->description); ?></p>
                    </div>
                </div>

                <!-- Product Info Grid -->
                <div class="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                    <!-- Vendor -->
                    <div class="flex items-center">
                        <span class="text-gray-500 font-medium">Vendor:</span>
                        <?php if($product->vendor): ?>
                            <a href="<?php echo e(route('vendors.storefront', $product->vendor->slug)); ?>" class="ml-2 font-medium text-gray-900 hover:text-black"><?php echo e($product->vendor->shop_name); ?></a>
                        <?php else: ?>
                            <span class="ml-2 text-gray-400">No vendor assigned</span>
                        <?php endif; ?>
                    </div>

                    <!-- Category -->
                    <?php if($product->category): ?>
                        <div class="flex items-center">
                            <span class="text-gray-500 font-medium">Category:</span>
                            <a href="<?php echo e(route('products.category', $product->category->slug)); ?>" class="ml-2 font-medium text-gray-900 hover:text-black"><?php echo e($product->category->name); ?></a>
                        </div>
                    <?php endif; ?>

                    <!-- Brand -->
                    <?php if($product->brand): ?>
                        <div class="flex items-center">
                            <span class="text-gray-500 font-medium">Brand:</span>
                            <span class="ml-2 font-medium text-gray-900"><?php echo e($product->brand->name); ?></span>
                        </div>
                    <?php endif; ?>

                    <!-- SKU -->
                    <?php if($product->sku): ?>
                        <div class="flex items-center">
                            <span class="text-gray-500 font-medium">SKU:</span>
                            <span class="ml-2 font-medium text-gray-900"><?php echo e($product->sku); ?></span>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Product Options Section -->
                <div class="mt-8">
                    <?php if($availableColors->isNotEmpty()): ?>
                        <!-- Color Selection -->
                        <div class="mb-6">
                            <h3 class="text-sm font-medium text-gray-900 mb-3">Color</h3>
                            <div class="flex flex-wrap gap-3">
                                <?php $__currentLoopData = $availableColors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($color && $color->id): ?>
                                        <button type="button"
                                                wire:click="selectColor(<?php echo e($color->id); ?>)"
                                                wire:loading.attr="disabled"
                                                wire:target="selectColor"
                                                class="group relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200 disabled:opacity-50 <?php echo e($selectedColorId === $color->id ? 'border-black shadow-lg scale-110' : 'border-gray-300 hover:border-gray-400'); ?>"
                                                style="background-color: <?php echo e($color->hex_code ?? '#f3f4f6'); ?>">
                                            <?php if($selectedColorId === $color->id): ?>
                                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                            <?php endif; ?>
                                            <span class="sr-only"><?php echo e($color->name); ?></span>
                                        </button>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Size Selection -->
                    <?php if($availableSizes->isNotEmpty()): ?>
                        <div class="mb-6">
                            <h3 class="text-sm font-medium text-gray-900 mb-3">Size</h3>
                            <div class="grid grid-cols-3 gap-2 sm:grid-cols-4 md:gap-3 lg:grid-cols-8">
                                <?php $__currentLoopData = $availableSizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($size && $size->id): ?>
                                        <button type="button"
                                                wire:click="selectSize(<?php echo e($size->id); ?>)"
                                                wire:loading.attr="disabled"
                                                wire:target="selectSize"
                                                class="group relative flex items-center justify-center rounded-md border py-2 px-2 text-xs sm:text-sm font-medium uppercase transition-all duration-200 focus:outline-none disabled:opacity-50
                                                    <?php echo e($selectedSizeId === $size->id
                                                        ? 'border-black bg-black text-white scale-105 shadow-lg'
                                                        : 'border-gray-300 text-gray-900 hover:bg-gray-50'); ?>">
                                            <?php echo e($size->name); ?>

                                        </button>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Variant Selection Warning -->
                    <?php if(($availableColors->isNotEmpty() || $availableSizes->isNotEmpty()) && !$selectedVariantId): ?>
                        <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm text-yellow-800">
                                    Please select
                                    <?php if($availableColors->isNotEmpty() && !$selectedColorId): ?>color@endif
                                    <?php if($availableColors->isNotEmpty() && $availableSizes->isNotEmpty() && (!$selectedColorId || !$selectedSizeId)): ?> and <?php endif; ?>
                                    <?php if($availableSizes->isNotEmpty() && !$selectedSizeId): ?>size<?php endif; ?>
                                    to continue
                                </span>
                            </div>
                        </div>
                    @endif

                    <!-- Selected Variant Info -->
                    <?php if($selectedVariantId && $selectedVariant): ?>
                        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Selected:</span>
                                <span class="text-sm font-medium text-gray-900"><?php echo e($selectedVariant->display_name); ?></span>
                            </div>
                            <div class="flex items-center justify-between mt-1">
                                <span class="text-sm text-gray-600">Stock:</span>
                                <span class="text-sm font-medium <?php echo e($selectedVariant->stock_quantity > 10 ? 'text-green-600' : ($selectedVariant->stock_quantity > 0 ? 'text-yellow-600' : 'text-red-600')); ?>">
                                    <?php echo e($selectedVariant->stock_quantity); ?> available
                                </span>
                            </div>
                            <?php if($selectedVariant->sku): ?>
                                <div class="flex items-center justify-between mt-1">
                                    <span class="text-sm text-gray-600">SKU:</span>
                                    <span class="text-sm font-medium text-gray-900"><?php echo e($selectedVariant->sku); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Quantity and Add to Cart Section -->
                    <div class="mt-8 flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
                        <!-- Quantity Controls -->
                        <div class="flex items-center border border-gray-300 rounded-md w-full sm:w-auto">
                            <button type="button"
                                    wire:click="decrementQuantity"
                                    wire:loading.attr="disabled"
                                    wire:target="decrementQuantity"
                                    class="px-3 sm:px-4 py-2 sm:py-3 text-gray-500 hover:bg-gray-100 rounded-l-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                <span wire:loading.remove wire:target="decrementQuantity">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                    </svg>
                                </span>
                                <span wire:loading wire:target="decrementQuantity">
                                    <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                            </button>
                            <input type="text" wire:model="quantity" class="w-16 sm:w-20 text-center border-0 focus:ring-0 text-sm sm:text-base font-medium" readonly>
                            <button type="button"
                                    wire:click="incrementQuantity"
                                    wire:loading.attr="disabled"
                                    wire:target="incrementQuantity"
                                    class="px-3 sm:px-4 py-2 sm:py-3 text-gray-500 hover:bg-gray-100 rounded-r-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                                <span wire:loading.remove wire:target="incrementQuantity">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </span>
                                <span wire:loading wire:target="incrementQuantity">
                                    <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>

                        <!-- Add to Cart Button -->
                        <?php
                            $hasVariants = $availableColors->isNotEmpty() || $availableSizes->isNotEmpty();
                            $variantRequired = $hasVariants && !$selectedVariantId;
                            $outOfStock = ($selectedVariant && !$selectedVariant->isAvailable()) || (!$selectedVariant && $product->stock < 1);
                            $isDisabled = $variantRequired || $outOfStock;
                        ?>

                        <button type="button"
                                wire:click="addToCart"
                                wire:loading.attr="disabled"
                                wire:target="addToCart"
                                <?php if($isDisabled): ?> disabled <?php endif; ?>
                                class="flex-1 bg-black border border-transparent rounded-md py-3 sm:py-4 px-6 sm:px-8 flex items-center justify-center text-sm sm:text-base font-medium text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                            <span wire:loading.remove wire:target="addToCart">
                                <?php if($variantRequired): ?>
                                    Select Options
                                <?php elseif($outOfStock): ?>
                                    Out of Stock
                                <?php else: ?>
                                    Add to Cart
                                <?php endif; ?>
                            </span>
                            <span wire:loading wire:target="addToCart" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Adding...
                            </span>
                        </button>
                    </div>

                    <!-- Wishlist Button -->
                    <div class="mt-6">
                        <button type="button"
                                wire:click="toggleWishlist"
                                wire:loading.attr="disabled"
                                wire:target="toggleWishlist"
                                class="w-full bg-white border border-gray-300 rounded-md py-3 px-8 flex items-center justify-center text-sm font-medium text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                            <span wire:loading.remove wire:target="toggleWishlist" class="flex items-center">
                                <svg class="w-5 h-5 mr-2 <?php echo e($inWishlist ? 'text-red-500 fill-current' : 'text-gray-400'); ?>" fill="<?php echo e($inWishlist ? 'currentColor' : 'none'); ?>" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                <?php echo e($inWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'); ?>

                            </span>
                            <span wire:loading wire:target="toggleWishlist" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <?php echo e($inWishlist ? 'Removing...' : 'Adding...'); ?>

                            </span>
                        </button>
                    </div>
                </div>

                <!-- Product Specifications -->
                <?php if($product->specifications && $product->specifications->count() > 0): ?>
                    <div class="mt-10">
                        <h3 class="text-lg font-medium text-gray-900">Specifications</h3>
                        <div class="mt-4">
                            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                                <?php $__currentLoopData = $product->specifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500"><?php echo e($spec->name); ?></dt>
                                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($spec->value); ?></dd>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </dl>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Reviews Section -->
                <?php if($product->reviews && $product->reviews->count() > 0): ?>
                    <div class="mt-10">
                        <h3 class="text-lg font-medium text-gray-900">Customer Reviews</h3>
                        <div class="mt-4 space-y-6">
                            <?php $__currentLoopData = $product->reviews->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border-b border-gray-200 pb-6">
                                    <div class="flex items-center">
                                        <div class="flex items-center">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <svg class="w-4 h-4 <?php echo e($i <= $review->rating ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                </svg>
                                            <?php endfor; ?>
                                        </div>
                                        <p class="ml-3 text-sm text-gray-600">by <?php echo e($review->user->name ?? 'Anonymous'); ?></p>
                                        <p class="ml-auto text-sm text-gray-500"><?php echo e($review->created_at->format('M j, Y')); ?></p>
                                    </div>
                                    <?php if($review->comment): ?>
                                        <div class="mt-4">
                                            <p class="text-sm text-gray-600"><?php echo e($review->comment); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\brandifyng\resources\views/livewire/product/show.blade.php ENDPATH**/ ?>